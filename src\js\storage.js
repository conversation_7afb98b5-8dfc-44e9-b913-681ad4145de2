// نظام تخزين البيانات للمتصفح باستخدام localStorage
class DataStorage {
    constructor() {
        this.storagePrefix = 'mustafa_kashaf_';
        this.initializeDataFiles();
    }

    // تهيئة ملفات البيانات الأساسية
    initializeDataFiles() {
        const defaultFiles = {
            'users.json': [],
            'products.json': [
                {
                    id: '1',
                    name: 'كنبة مودرن 3 مقاعد',
                    category: 'كنب',
                    price: 2500,
                    cost: 1800,
                    stock: 10,
                    description: 'كنبة مودرن عالية الجودة بتصميم أنيق',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: '2',
                    name: 'طاولة طعام خشبية',
                    category: 'طاولات',
                    price: 1200,
                    cost: 800,
                    stock: 5,
                    description: 'طاولة طعام من الخشب الطبيعي لـ 6 أشخاص',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ],
            'customers.json': [
                {
                    id: '1',
                    name: 'أحمد محمد',
                    phone: '01234567890',
                    address: 'القاهرة، مصر الجديدة',
                    email: '<EMAIL>',
                    createdAt: new Date().toISOString()
                }
            ],
            'invoices.json': [],
            'settings.json': {
                storeName: 'مصطفي كشاف للمفروشات',
                storeAddress: 'القاهرة، مصر',
                storePhone: '02-12345678',
                storeEmail: '<EMAIL>',
                taxRate: 14,
                currency: 'جنيه مصري',
                invoicePrefix: 'INV-',
                nextInvoiceNumber: 1001
            }
        };

        // تهيئة البيانات الافتراضية إذا لم تكن موجودة
        Object.keys(defaultFiles).forEach(filename => {
            if (!this.readFile(filename)) {
                this.writeFile(filename, defaultFiles[filename]);
            }
        });
    }

    // قراءة ملف JSON من localStorage
    readFile(filename) {
        try {
            const key = this.storagePrefix + filename;
            const data = localStorage.getItem(key);
            if (data) {
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`خطأ في قراءة الملف ${filename}:`, error);
            return null;
        }
    }

    // كتابة ملف JSON إلى localStorage
    writeFile(filename, data) {
        try {
            const key = this.storagePrefix + filename;
            localStorage.setItem(key, JSON.stringify(data, null, 2));
            return true;
        } catch (error) {
            console.error(`خطأ في كتابة الملف ${filename}:`, error);
            return false;
        }
    }

    // إضافة عنصر جديد
    addItem(filename, item) {
        const data = this.readFile(filename) || [];
        if (Array.isArray(data)) {
            data.push(item);
            return this.writeFile(filename, data);
        }
        return false;
    }

    // تحديث عنصر
    updateItem(filename, id, updatedItem) {
        const data = this.readFile(filename);
        if (Array.isArray(data)) {
            const index = data.findIndex(item => item.id === id);
            if (index !== -1) {
                data[index] = { ...data[index], ...updatedItem };
                return this.writeFile(filename, data);
            }
        }
        return false;
    }

    // حذف عنصر
    deleteItem(filename, id) {
        const data = this.readFile(filename);
        if (Array.isArray(data)) {
            const filteredData = data.filter(item => item.id !== id);
            return this.writeFile(filename, filteredData);
        }
        return false;
    }

    // البحث في البيانات
    searchItems(filename, searchTerm, fields = []) {
        const data = this.readFile(filename);
        if (!Array.isArray(data) || !searchTerm) {
            return data || [];
        }

        return data.filter(item => {
            if (fields.length === 0) {
                // البحث في جميع الحقول
                return Object.values(item).some(value =>
                    String(value).toLowerCase().includes(searchTerm.toLowerCase())
                );
            } else {
                // البحث في حقول محددة
                return fields.some(field =>
                    item[field] && String(item[field]).toLowerCase().includes(searchTerm.toLowerCase())
                );
            }
        });
    }

    // الحصول على عنصر بالمعرف
    getItemById(filename, id) {
        const data = this.readFile(filename);
        if (Array.isArray(data)) {
            return data.find(item => item.id === id);
        }
        return null;
    }

    // إنشاء نسخة احتياطية في localStorage
    createBackup() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupKey = this.storagePrefix + 'backup_' + timestamp;

            // جمع جميع البيانات
            const allData = {};
            const files = ['users.json', 'products.json', 'customers.json', 'invoices.json', 'settings.json'];

            files.forEach(filename => {
                const data = this.readFile(filename);
                if (data) {
                    allData[filename] = data;
                }
            });

            // حفظ النسخة الاحتياطية
            localStorage.setItem(backupKey, JSON.stringify(allData));

            // الاحتفاظ بآخر 5 نسخ احتياطية فقط
            this.cleanupOldBackups();

            return backupKey;
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return null;
        }
    }

    // تنظيف النسخ الاحتياطية القديمة
    cleanupOldBackups() {
        try {
            const backupKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.storagePrefix + 'backup_')) {
                    backupKeys.push(key);
                }
            }

            // ترتيب النسخ حسب التاريخ والاحتفاظ بآخر 5 نسخ
            backupKeys.sort().reverse();
            if (backupKeys.length > 5) {
                for (let i = 5; i < backupKeys.length; i++) {
                    localStorage.removeItem(backupKeys[i]);
                }
            }
        } catch (error) {
            console.error('خطأ في تنظيف النسخ الاحتياطية:', error);
        }
    }

    // الحصول على إحصائيات
    getStatistics() {
        const products = this.readFile('products.json') || [];
        const customers = this.readFile('customers.json') || [];
        const invoices = this.readFile('invoices.json') || [];

        const totalProducts = products.length;
        const totalCustomers = customers.length;
        const totalInvoices = invoices.length;

        const totalSales = invoices.reduce((sum, invoice) => sum + (invoice.total || 0), 0);
        const totalProfit = invoices.reduce((sum, invoice) => sum + (invoice.profit || 0), 0);

        const lowStockProducts = products.filter(product => product.stock <= 5);

        return {
            totalProducts,
            totalCustomers,
            totalInvoices,
            totalSales,
            totalProfit,
            lowStockProducts: lowStockProducts.length,
            lowStockItems: lowStockProducts
        };
    }

    // استعادة نسخة احتياطية
    restoreBackup(backupKey) {
        try {
            const backupData = localStorage.getItem(backupKey);
            if (!backupData) {
                return false;
            }

            const allData = JSON.parse(backupData);

            // استعادة جميع الملفات
            Object.keys(allData).forEach(filename => {
                this.writeFile(filename, allData[filename]);
            });

            return true;
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return false;
        }
    }

    // الحصول على قائمة النسخ الاحتياطية
    getBackupList() {
        const backups = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.storagePrefix + 'backup_')) {
                const timestamp = key.replace(this.storagePrefix + 'backup_', '');
                backups.push({
                    key: key,
                    timestamp: timestamp,
                    date: new Date(timestamp.replace(/-/g, ':')).toLocaleString('ar-EG')
                });
            }
        }
        return backups.sort((a, b) => b.timestamp.localeCompare(a.timestamp));
    }

    // مسح جميع البيانات
    clearAllData() {
        try {
            const keys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.storagePrefix)) {
                    keys.push(key);
                }
            }

            keys.forEach(key => localStorage.removeItem(key));
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    }
}

// إنشاء مثيل واحد من كلاس التخزين
const storage = new DataStorage();

// تصدير للاستخدام العام
window.storage = storage;
