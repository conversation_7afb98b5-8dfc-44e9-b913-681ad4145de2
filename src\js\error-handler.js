// معالج الأخطاء العام للتطبيق
class ErrorHandler {
    constructor() {
        this.setupGlobalErrorHandling();
        this.errors = [];
    }

    // إعداد معالجة الأخطاء العامة
    setupGlobalErrorHandling() {
        // معالجة أخطاء JavaScript
        window.addEventListener('error', (event) => {
            this.logError('JavaScript Error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });

        // معالجة الوعود المرفوضة
        window.addEventListener('unhandledrejection', (event) => {
            this.logError('Unhandled Promise Rejection', {
                reason: event.reason
            });
        });
    }

    // تسجيل الأخطاء
    logError(type, details) {
        const error = {
            type: type,
            details: details,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        this.errors.push(error);
        console.error(`[${type}]`, details);

        // حفظ الأخطاء في localStorage
        try {
            const savedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
            savedErrors.push(error);
            
            // الاحتفاظ بآخر 50 خطأ فقط
            if (savedErrors.length > 50) {
                savedErrors.splice(0, savedErrors.length - 50);
            }
            
            localStorage.setItem('app_errors', JSON.stringify(savedErrors));
        } catch (e) {
            console.error('فشل في حفظ الخطأ:', e);
        }
    }

    // عرض رسالة خطأ للمستخدم
    showUserError(message, type = 'error') {
        const errorDiv = document.createElement('div');
        errorDiv.className = `user-error ${type}`;
        errorDiv.innerHTML = `
            <div class="error-content">
                <i class="fas ${type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="error-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // إضافة الأنماط إذا لم تكن موجودة
        this.addErrorStyles();

        // إضافة الرسالة إلى الصفحة
        document.body.appendChild(errorDiv);

        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }

    // إضافة أنماط رسائل الأخطاء
    addErrorStyles() {
        if (!document.getElementById('errorStyles')) {
            const style = document.createElement('style');
            style.id = 'errorStyles';
            style.textContent = `
                .user-error {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10001;
                    max-width: 400px;
                    background: white;
                    border-radius: 10px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    animation: slideInRight 0.3s ease-out;
                }
                
                .user-error.error {
                    border-left: 4px solid #dc2626;
                }
                
                .user-error.info {
                    border-left: 4px solid #3b82f6;
                }
                
                .user-error.success {
                    border-left: 4px solid #10b981;
                }
                
                .error-content {
                    padding: 15px 20px;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                }
                
                .error-content i {
                    font-size: 1.2rem;
                }
                
                .user-error.error .error-content i {
                    color: #dc2626;
                }
                
                .user-error.info .error-content i {
                    color: #3b82f6;
                }
                
                .user-error.success .error-content i {
                    color: #10b981;
                }
                
                .error-content span {
                    flex: 1;
                    color: #333;
                    font-weight: 500;
                }
                
                .error-close {
                    background: none;
                    border: none;
                    color: #999;
                    cursor: pointer;
                    padding: 5px;
                    border-radius: 50%;
                    transition: all 0.3s ease;
                }
                
                .error-close:hover {
                    background: #f1f5f9;
                    color: #667eea;
                }
                
                @keyframes slideInRight {
                    from {
                        opacity: 0;
                        transform: translateX(100%);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // الحصول على قائمة الأخطاء المحفوظة
    getSavedErrors() {
        try {
            return JSON.parse(localStorage.getItem('app_errors') || '[]');
        } catch (e) {
            return [];
        }
    }

    // مسح الأخطاء المحفوظة
    clearSavedErrors() {
        try {
            localStorage.removeItem('app_errors');
            this.errors = [];
            return true;
        } catch (e) {
            return false;
        }
    }

    // فحص حالة التطبيق
    checkAppHealth() {
        const health = {
            storage: false,
            dom: false,
            javascript: false
        };

        try {
            // فحص التخزين
            if (typeof storage !== 'undefined' && storage.readFile) {
                health.storage = true;
            }

            // فحص DOM
            if (document.getElementById('pageContent')) {
                health.dom = true;
            }

            // فحص JavaScript
            health.javascript = true;
        } catch (e) {
            this.logError('Health Check Failed', e);
        }

        return health;
    }
}

// إنشاء مثيل من معالج الأخطاء
const errorHandler = new ErrorHandler();

// تصدير للاستخدام العام
window.errorHandler = errorHandler;

// دوال مساعدة عامة
window.showError = (message) => errorHandler.showUserError(message, 'error');
window.showInfo = (message) => errorHandler.showUserError(message, 'info');
window.showSuccess = (message) => errorHandler.showUserError(message, 'success');
