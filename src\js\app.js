// التطبيق الرئيسي
class App {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        // التحقق من الجلسة المحفوظة عند بدء التطبيق
        if (authManager.checkSavedSession()) {
            authManager.showMainApp();
            this.loadDashboard();
        } else {
            authManager.showLoginScreen();
        }

        // إعداد أحداث القائمة الجانبية
        this.setupSidebarEvents();
    }

    setupSidebarEvents() {
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', () => {
                // إزالة الفئة النشطة من جميع العناصر
                menuItems.forEach(mi => mi.classList.remove('active'));
                
                // إضافة الفئة النشطة للعنصر المحدد
                item.classList.add('active');
                
                // تحميل الصفحة المطلوبة
                const page = item.getAttribute('data-page');
                this.loadPage(page);
            });
        });
    }

    loadPage(page) {
        this.currentPage = page;
        const pageContent = document.getElementById('pageContent');
        
        switch (page) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'products':
                this.loadProductsPage();
                break;
            case 'customers':
                this.loadCustomersPage();
                break;
            case 'invoices':
                this.loadInvoicesPage();
                break;
            case 'reports':
                this.loadReportsPage();
                break;
            case 'activity':
                this.loadActivityPage();
                break;
            case 'settings':
                this.loadSettingsPage();
                break;
            default:
                this.loadDashboard();
        }
    }

    loadDashboard() {
        const stats = storage.getStatistics();
        const recentInvoices = storage.readFile('invoices.json')?.slice(-5) || [];
        
        const dashboardHTML = `
            <div class="dashboard fade-in">
                <div class="page-header">
                    <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
                    <p>نظرة عامة على أداء المعرض</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon products">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.totalProducts}</h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon customers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.totalCustomers}</h3>
                            <p>إجمالي العملاء</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon invoices">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.totalInvoices}</h3>
                            <p>إجمالي الفواتير</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon sales">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.totalSales.toLocaleString()} جنيه</h3>
                            <p>إجمالي المبيعات</p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-content">
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h2><i class="fas fa-exclamation-triangle"></i> تنبيهات المخزون</h2>
                        </div>
                        <div class="low-stock-alerts">
                            ${stats.lowStockItems.length > 0 ?
                                stats.lowStockItems.map(product => `
                                    <div class="alert-item ${product.stock === 0 ? 'critical' : product.stock <= 2 ? 'warning' : 'low'}">
                                        <i class="fas ${product.stock === 0 ? 'fa-times-circle' : 'fa-exclamation-triangle'}"></i>
                                        <div class="alert-content">
                                            <span class="product-name">${product.name}</span>
                                            <span class="stock-info">
                                                ${product.stock === 0 ? 'نفد من المخزون' :
                                                  product.stock <= 2 ? `متبقي ${product.stock} قطع فقط - يحتاج تجديد عاجل` :
                                                  `متبقي ${product.stock} قطع - قريب من النفاد`}
                                            </span>
                                        </div>
                                        <button class="alert-action" onclick="app.loadPage('products')" title="إدارة المنتج">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                `).join('') :
                                '<div class="no-alerts">✅ جميع المنتجات متوفرة بكميات كافية</div>'
                            }
                        </div>
                    </div>

                    <div class="dashboard-section">
                        <div class="section-header">
                            <h2><i class="fas fa-clock"></i> آخر الفواتير</h2>
                        </div>
                        <div class="recent-invoices">
                            ${recentInvoices.length > 0 ? 
                                recentInvoices.reverse().map(invoice => `
                                    <div class="invoice-item">
                                        <div class="invoice-info">
                                            <strong>${invoice.invoiceNumber}</strong>
                                            <span>${invoice.customerName}</span>
                                        </div>
                                        <div class="invoice-amount">
                                            ${invoice.total?.toLocaleString()} جنيه
                                        </div>
                                    </div>
                                `).join('') : 
                                '<div class="no-invoices">لا توجد فواتير حديثة</div>'
                            }
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h2><i class="fas fa-bolt"></i> إجراءات سريعة</h2>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="app.loadPage('invoices')">
                            <i class="fas fa-plus"></i>
                            فاتورة جديدة
                        </button>
                        <button class="action-btn" onclick="app.loadPage('products')">
                            <i class="fas fa-box"></i>
                            إضافة منتج
                        </button>
                        <button class="action-btn" onclick="app.loadPage('customers')">
                            <i class="fas fa-user-plus"></i>
                            عميل جديد
                        </button>
                        <button class="action-btn" onclick="app.loadPage('reports')">
                            <i class="fas fa-chart-bar"></i>
                            عرض التقارير
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('pageContent').innerHTML = dashboardHTML;
        this.addDashboardStyles();
    }

    loadProductsPage() {
        try {
            document.getElementById('pageContent').innerHTML = `
                <div class="products-page fade-in">
                    <div class="page-header">
                        <h1><i class="fas fa-box"></i> إدارة المنتجات</h1>
                        <button class="btn btn-primary" onclick="showAddProductModal()">
                            <i class="fas fa-plus"></i> إضافة منتج جديد
                        </button>
                    </div>
                    <div id="productsContent">
                        <!-- سيتم تحميل المنتجات هنا -->
                    </div>
                </div>
            `;

            // التأكد من تحميل المنتجات
            setTimeout(() => {
                if (typeof loadProducts === 'function') {
                    loadProducts();
                } else {
                    console.error('دالة loadProducts غير موجودة');
                    if (typeof showError === 'function') {
                        showError('خطأ في تحميل صفحة المنتجات');
                    }
                }
            }, 100);
        } catch (error) {
            console.error('خطأ في تحميل صفحة المنتجات:', error);
            if (typeof showError === 'function') {
                showError('حدث خطأ في تحميل صفحة المنتجات');
            }
        }
    }

    loadCustomersPage() {
        document.getElementById('pageContent').innerHTML = `
            <div class="customers-page fade-in">
                <div class="page-header">
                    <h1><i class="fas fa-users"></i> إدارة العملاء</h1>
                    <button class="btn btn-primary" onclick="showAddCustomerModal()">
                        <i class="fas fa-user-plus"></i> إضافة عميل جديد
                    </button>
                </div>
                <div id="customersContent">
                    <!-- سيتم تحميل العملاء هنا -->
                </div>
            </div>
        `;
        if (typeof loadCustomers === 'function') {
            loadCustomers();
        }
    }

    loadInvoicesPage() {
        document.getElementById('pageContent').innerHTML = `
            <div class="invoices-page fade-in">
                <div class="page-header">
                    <h1><i class="fas fa-file-invoice"></i> إدارة الفواتير</h1>
                    <button class="btn btn-primary" onclick="showNewInvoiceModal()">
                        <i class="fas fa-plus"></i> فاتورة جديدة
                    </button>
                </div>
                <div id="invoicesContent">
                    <!-- سيتم تحميل الفواتير هنا -->
                </div>
            </div>
        `;
        if (typeof loadInvoices === 'function') {
            loadInvoices();
        }
    }

    loadReportsPage() {
        const stats = storage.getStatistics();

        document.getElementById('pageContent').innerHTML = `
            <div class="reports-page fade-in">
                <div class="page-header">
                    <h1><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h1>
                    <div class="reports-actions">
                        <button class="btn btn-primary" onclick="exportReports()">
                            <i class="fas fa-download"></i> تصدير التقارير
                        </button>
                        <button class="btn btn-secondary" onclick="refreshReports()">
                            <i class="fas fa-sync"></i> تحديث
                        </button>
                    </div>
                </div>

                <div class="reports-summary">
                    <div class="summary-card">
                        <div class="summary-icon sales">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="summary-content">
                            <h3>${stats.totalSales.toLocaleString()} جنيه</h3>
                            <p>إجمالي المبيعات</p>
                        </div>
                    </div>

                    <div class="summary-card">
                        <div class="summary-icon profit">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="summary-content">
                            <h3>${stats.totalProfit.toLocaleString()} جنيه</h3>
                            <p>إجمالي الأرباح</p>
                        </div>
                    </div>

                    <div class="summary-card">
                        <div class="summary-icon invoices">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="summary-content">
                            <h3>${stats.totalInvoices}</h3>
                            <p>عدد الفواتير</p>
                        </div>
                    </div>

                    <div class="summary-card">
                        <div class="summary-icon customers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="summary-content">
                            <h3>${stats.totalCustomers}</h3>
                            <p>عدد العملاء</p>
                        </div>
                    </div>
                </div>

                <div class="charts-grid">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-chart-pie"></i>
                            المبيعات حسب الفئة
                        </div>
                        <div id="categoryChart"></div>
                    </div>

                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-chart-bar"></i>
                            المبيعات الشهرية
                        </div>
                        <div id="monthlySalesChart"></div>
                    </div>

                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-chart-line"></i>
                            الأرباح اليومية (30 يوم)
                        </div>
                        <div id="profitChart"></div>
                    </div>

                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-boxes"></i>
                            حالة المخزون
                        </div>
                        <div id="inventoryChart"></div>
                    </div>
                </div>
            </div>
        `;

        this.addReportsStyles();

        // تحميل الرسوم البيانية
        setTimeout(() => {
            if (typeof chartsManager !== 'undefined') {
                chartsManager.addChartStyles();
                chartsManager.createCategoryPieChart('categoryChart');
                chartsManager.createMonthlySalesChart('monthlySalesChart');
                chartsManager.createProfitLineChart('profitChart');
                chartsManager.createInventoryStats('inventoryChart');
            }
        }, 100);
    }

    loadActivityPage() {
        const logs = typeof activityLogger !== 'undefined' ? activityLogger.getLogs(50) : [];
        const stats = typeof activityLogger !== 'undefined' ? activityLogger.getLogStats() : {};

        document.getElementById('pageContent').innerHTML = `
            <div class="activity-page fade-in">
                <div class="page-header">
                    <h1><i class="fas fa-history"></i> سجل الأنشطة</h1>
                    <div class="activity-actions">
                        <button class="btn btn-primary" onclick="createManualBackup()">
                            <i class="fas fa-save"></i> نسخة احتياطية
                        </button>
                        <button class="btn btn-secondary" onclick="exportActivityLogs()">
                            <i class="fas fa-download"></i> تصدير السجل
                        </button>
                    </div>
                </div>

                <div class="activity-stats">
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.total || 0}</h3>
                            <p>إجمالي الأنشطة</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.today || 0}</h3>
                            <p>أنشطة اليوم</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-info">
                            <h3>${stats.byLevel?.error || 0}</h3>
                            <p>الأخطاء</p>
                        </div>
                    </div>
                </div>

                <div class="activity-log">
                    <div class="log-header">
                        <h2><i class="fas fa-stream"></i> آخر الأنشطة</h2>
                        <div class="log-filters">
                            <select id="levelFilter" onchange="filterActivityLogs()">
                                <option value="">جميع المستويات</option>
                                <option value="success">نجح</option>
                                <option value="info">معلومات</option>
                                <option value="warning">تحذير</option>
                                <option value="error">خطأ</option>
                            </select>
                        </div>
                    </div>

                    <div class="log-entries" id="logEntries">
                        ${logs.length > 0 ? logs.map(log => `
                            <div class="log-entry ${log.level}">
                                <div class="log-icon">
                                    <i class="fas ${this.getLogIcon(log.level)}"></i>
                                </div>
                                <div class="log-content">
                                    <div class="log-action">${log.action}</div>
                                    <div class="log-details">${JSON.stringify(log.details)}</div>
                                    <div class="log-meta">
                                        <span class="log-user">${log.user}</span>
                                        <span class="log-time">${log.date} ${log.time}</span>
                                    </div>
                                </div>
                            </div>
                        `).join('') : '<div class="no-logs">لا توجد أنشطة مسجلة</div>'}
                    </div>
                </div>
            </div>
        `;
        this.addActivityStyles();
    }

    getLogIcon(level) {
        switch (level) {
            case 'success': return 'fa-check-circle';
            case 'warning': return 'fa-exclamation-triangle';
            case 'error': return 'fa-times-circle';
            default: return 'fa-info-circle';
        }
    }

    loadSettingsPage() {
        const settings = storage.readFile('settings.json') || {};
        document.getElementById('pageContent').innerHTML = `
            <div class="settings-page fade-in">
                <div class="page-header">
                    <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
                </div>
                <div class="settings-content">
                    <div class="settings-section">
                        <h3>معلومات المعرض</h3>
                        <div class="form-group">
                            <label>اسم المعرض</label>
                            <input type="text" id="storeName" value="${settings.storeName || ''}" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>العنوان</label>
                            <input type="text" id="storeAddress" value="${settings.storeAddress || ''}" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="text" id="storePhone" value="${settings.storePhone || ''}" class="form-control">
                        </div>
                        <button class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    addDashboardStyles() {
        if (!document.getElementById('dashboardStyles')) {
            const style = document.createElement('style');
            style.id = 'dashboardStyles';
            style.textContent = `
                .dashboard { padding: 0; }
                .page-header { margin-bottom: 30px; }
                .page-header h1 { font-size: 2rem; color: #333; margin-bottom: 5px; }
                .page-header p { color: #666; }
                .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
                .stat-card { background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); display: flex; align-items: center; gap: 20px; }
                .stat-icon { width: 60px; height: 60px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; color: white; }
                .stat-icon.products { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
                .stat-icon.customers { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
                .stat-icon.invoices { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
                .stat-icon.sales { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
                .stat-info h3 { font-size: 1.8rem; font-weight: 700; color: #333; margin-bottom: 5px; }
                .stat-info p { color: #666; font-size: 0.9rem; }
                .dashboard-content { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px; }
                .dashboard-section { background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
                .section-header h2 { font-size: 1.3rem; color: #333; margin-bottom: 20px; }
                .invoice-item { padding: 12px; border-bottom: 1px solid #eee; display: flex; align-items: center; justify-content: space-between; }
                .alert-item { padding: 15px; border-bottom: 1px solid #eee; display: flex; align-items: center; gap: 15px; border-radius: 8px; margin-bottom: 10px; transition: all 0.3s ease; }
                .alert-item:hover { transform: translateX(-5px); box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
                .alert-item.critical { background: #fee2e2; border-left: 4px solid #dc2626; }
                .alert-item.warning { background: #fef3c7; border-left: 4px solid #f59e0b; }
                .alert-item.low { background: #dbeafe; border-left: 4px solid #3b82f6; }
                .alert-item.critical i { color: #dc2626; }
                .alert-item.warning i { color: #f59e0b; }
                .alert-item.low i { color: #3b82f6; }
                .alert-content { flex: 1; }
                .alert-content .product-name { font-weight: 600; color: #333; display: block; margin-bottom: 4px; }
                .alert-content .stock-info { font-size: 0.9rem; color: #666; }
                .alert-action { background: none; border: none; color: #667eea; cursor: pointer; padding: 8px; border-radius: 6px; transition: all 0.3s ease; }
                .alert-action:hover { background: rgba(102, 126, 234, 0.1); }
                .no-alerts, .no-invoices { text-align: center; color: #666; padding: 20px; }
                .quick-actions { background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
                .quick-actions h2 { font-size: 1.3rem; color: #333; margin-bottom: 20px; }
                .action-buttons { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
                .action-btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 15px 20px; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px; font-weight: 500; }
                .action-btn:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3); }
            `;
            document.head.appendChild(style);
        }
    }

    addActivityStyles() {
        if (!document.getElementById('activityStyles')) {
            const style = document.createElement('style');
            style.id = 'activityStyles';
            style.textContent = `
                .activity-page { padding: 0; }
                .activity-actions { display: flex; gap: 10px; }
                .activity-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
                .activity-log { background: white; border-radius: 15px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
                .log-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
                .log-header h2 { margin: 0; color: #333; }
                .log-filters select { padding: 8px 12px; border: 2px solid #e1e5e9; border-radius: 8px; }
                .log-entries { max-height: 500px; overflow-y: auto; }
                .log-entry { display: flex; align-items: flex-start; gap: 15px; padding: 15px; border-bottom: 1px solid #eee; transition: background 0.3s ease; }
                .log-entry:hover { background: #f8f9fa; }
                .log-entry.success { border-left: 4px solid #38a169; }
                .log-entry.info { border-left: 4px solid #3182ce; }
                .log-entry.warning { border-left: 4px solid #d69e2e; }
                .log-entry.error { border-left: 4px solid #e53e3e; }
                .log-icon { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; }
                .log-entry.success .log-icon { background: #38a169; }
                .log-entry.info .log-icon { background: #3182ce; }
                .log-entry.warning .log-icon { background: #d69e2e; }
                .log-entry.error .log-icon { background: #e53e3e; }
                .log-content { flex: 1; }
                .log-action { font-weight: 600; color: #333; margin-bottom: 5px; }
                .log-details { font-size: 0.9rem; color: #666; margin-bottom: 8px; }
                .log-meta { display: flex; gap: 15px; font-size: 0.8rem; color: #999; }
                .no-logs { text-align: center; padding: 40px; color: #666; }
            `;
            document.head.appendChild(style);
        }
    }

    addReportsStyles() {
        if (!document.getElementById('reportsStyles')) {
            const style = document.createElement('style');
            style.id = 'reportsStyles';
            style.textContent = `
                .reports-page { padding: 0; }
                .reports-actions { display: flex; gap: 10px; }
                .reports-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
                .summary-card { background: white; padding: 20px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); display: flex; align-items: center; gap: 15px; }
                .summary-icon { width: 50px; height: 50px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; color: white; }
                .summary-icon.sales { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
                .summary-icon.profit { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
                .summary-icon.invoices { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
                .summary-icon.customers { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
                .summary-content h3 { font-size: 1.5rem; font-weight: 700; color: #333; margin: 0 0 5px 0; }
                .summary-content p { color: #666; margin: 0; font-size: 0.9rem; }
                .charts-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
            `;
            document.head.appendChild(style);
        }
    }

    getCurrentPage() {
        return this.currentPage;
    }
}

// إنشاء مثيل من التطبيق
const app = new App();

// دوال مساعدة
function saveSettings() {
    const settings = {
        storeName: document.getElementById('storeName').value,
        storeAddress: document.getElementById('storeAddress').value,
        storePhone: document.getElementById('storePhone').value
    };
    
    if (storage.writeFile('settings.json', settings)) {
        alert('تم حفظ الإعدادات بنجاح');
    } else {
        alert('حدث خطأ أثناء حفظ الإعدادات');
    }
}

// تحميل لوحة التحكم (دالة مساعدة)
function loadDashboard() {
    app.loadDashboard();
}
