// نظام الإشعارات والرسائل المحسن
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.init();
    }

    init() {
        this.createContainer();
        this.addStyles();
    }

    // إنشاء حاوية الإشعارات
    createContainer() {
        if (!document.getElementById('notificationContainer')) {
            const container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'notification-container';
            document.body.appendChild(container);
            this.container = container;
        }
    }

    // إظهار رسالة نجاح
    showSuccess(message, duration = 4000) {
        return this.showNotification(message, 'success', duration);
    }

    // إظهار رسالة خطأ
    showError(message, duration = 5000) {
        return this.showNotification(message, 'error', duration);
    }

    // إظهار رسالة تحذير
    showWarning(message, duration = 4000) {
        return this.showNotification(message, 'warning', duration);
    }

    // إظهار رسالة معلومات
    showInfo(message, duration = 4000) {
        return this.showNotification(message, 'info', duration);
    }

    // إظهار إشعار عام
    showNotification(message, type = 'info', duration = 4000) {
        const notification = this.createNotification(message, type);
        this.container.appendChild(notification);
        this.notifications.push(notification);

        // تأثير الظهور
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // إزالة تلقائية
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }

        return notification;
    }

    // إنشاء عنصر الإشعار
    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icon = this.getIcon(type);
        
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas ${icon}"></i>
                </div>
                <div class="notification-message">${message}</div>
                <button class="notification-close" onclick="notificationSystem.removeNotification(this.parentElement.parentElement)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-progress"></div>
        `;

        return notification;
    }

    // الحصول على الأيقونة المناسبة
    getIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // إزالة إشعار
    removeNotification(notification) {
        if (notification && notification.parentElement) {
            notification.classList.add('hide');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.parentElement.removeChild(notification);
                }
                const index = this.notifications.indexOf(notification);
                if (index > -1) {
                    this.notifications.splice(index, 1);
                }
            }, 300);
        }
    }

    // إزالة جميع الإشعارات
    clearAll() {
        this.notifications.forEach(notification => {
            this.removeNotification(notification);
        });
    }

    // إظهار نافذة تأكيد
    showConfirm(message, onConfirm, onCancel = null, options = {}) {
        const modal = this.createConfirmModal(message, onConfirm, onCancel, options);
        document.body.appendChild(modal);
        
        setTimeout(() => {
            modal.classList.add('show');
        }, 100);

        return modal;
    }

    // إنشاء نافذة التأكيد
    createConfirmModal(message, onConfirm, onCancel, options) {
        const {
            title = 'تأكيد العملية',
            confirmText = 'تأكيد',
            cancelText = 'إلغاء',
            type = 'warning'
        } = options;

        const modal = document.createElement('div');
        modal.className = 'confirm-modal';
        modal.innerHTML = `
            <div class="confirm-overlay"></div>
            <div class="confirm-content">
                <div class="confirm-header">
                    <div class="confirm-icon confirm-${type}">
                        <i class="fas ${this.getIcon(type)}"></i>
                    </div>
                    <h3>${title}</h3>
                </div>
                <div class="confirm-body">
                    <p>${message}</p>
                </div>
                <div class="confirm-actions">
                    <button class="btn btn-secondary confirm-cancel">${cancelText}</button>
                    <button class="btn btn-primary confirm-ok">${confirmText}</button>
                </div>
            </div>
        `;

        // معالجة الأحداث
        const cancelBtn = modal.querySelector('.confirm-cancel');
        const confirmBtn = modal.querySelector('.confirm-ok');
        const overlay = modal.querySelector('.confirm-overlay');

        const closeModal = () => {
            modal.classList.add('hide');
            setTimeout(() => {
                if (modal.parentElement) {
                    modal.parentElement.removeChild(modal);
                }
            }, 300);
        };

        cancelBtn.addEventListener('click', () => {
            closeModal();
            if (onCancel) onCancel();
        });

        confirmBtn.addEventListener('click', () => {
            closeModal();
            if (onConfirm) onConfirm();
        });

        overlay.addEventListener('click', () => {
            closeModal();
            if (onCancel) onCancel();
        });

        // إغلاق بـ Escape
        const handleKeydown = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                if (onCancel) onCancel();
                document.removeEventListener('keydown', handleKeydown);
            }
        };
        document.addEventListener('keydown', handleKeydown);

        return modal;
    }

    // إضافة الأنماط
    addStyles() {
        if (!document.getElementById('notificationStyles')) {
            const style = document.createElement('style');
            style.id = 'notificationStyles';
            style.textContent = `
                /* حاوية الإشعارات */
                .notification-container {
                    position: fixed;
                    top: 80px;
                    left: 20px;
                    z-index: 10000;
                    pointer-events: none;
                }

                /* الإشعار */
                .notification {
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
                    margin-bottom: 12px;
                    min-width: 350px;
                    max-width: 450px;
                    opacity: 0;
                    transform: translateX(-100%);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    pointer-events: auto;
                    overflow: hidden;
                    border-left: 4px solid;
                }

                .notification.show {
                    opacity: 1;
                    transform: translateX(0);
                }

                .notification.hide {
                    opacity: 0;
                    transform: translateX(-100%);
                }

                /* أنواع الإشعارات */
                .notification-success {
                    border-left-color: #10b981;
                }

                .notification-error {
                    border-left-color: #ef4444;
                }

                .notification-warning {
                    border-left-color: #f59e0b;
                }

                .notification-info {
                    border-left-color: #3b82f6;
                }

                /* محتوى الإشعار */
                .notification-content {
                    display: flex;
                    align-items: center;
                    padding: 16px 20px;
                    gap: 12px;
                }

                .notification-icon {
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    font-size: 14px;
                    color: white;
                }

                .notification-success .notification-icon {
                    background: #10b981;
                }

                .notification-error .notification-icon {
                    background: #ef4444;
                }

                .notification-warning .notification-icon {
                    background: #f59e0b;
                }

                .notification-info .notification-icon {
                    background: #3b82f6;
                }

                .notification-message {
                    flex: 1;
                    color: #374151;
                    font-weight: 500;
                    line-height: 1.4;
                }

                .notification-close {
                    background: none;
                    border: none;
                    color: #9ca3af;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 4px;
                    transition: all 0.2s ease;
                }

                .notification-close:hover {
                    background: #f3f4f6;
                    color: #6b7280;
                }

                /* شريط التقدم */
                .notification-progress {
                    height: 3px;
                    background: rgba(0, 0, 0, 0.1);
                    position: relative;
                    overflow: hidden;
                }

                .notification-progress::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    height: 100%;
                    width: 100%;
                    background: currentColor;
                    transform: translateX(-100%);
                    animation: progress 4s linear forwards;
                }

                .notification-success .notification-progress::after {
                    background: #10b981;
                }

                .notification-error .notification-progress::after {
                    background: #ef4444;
                }

                .notification-warning .notification-progress::after {
                    background: #f59e0b;
                }

                .notification-info .notification-progress::after {
                    background: #3b82f6;
                }

                @keyframes progress {
                    to {
                        transform: translateX(0);
                    }
                }

                /* نوافذ التأكيد */
                .confirm-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 10001;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }

                .confirm-modal.show {
                    opacity: 1;
                    visibility: visible;
                }

                .confirm-modal.hide {
                    opacity: 0;
                    visibility: hidden;
                }

                .confirm-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    backdrop-filter: blur(4px);
                }

                .confirm-content {
                    background: white;
                    border-radius: 16px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
                    max-width: 400px;
                    width: 90%;
                    position: relative;
                    transform: scale(0.9);
                    transition: transform 0.3s ease;
                }

                .confirm-modal.show .confirm-content {
                    transform: scale(1);
                }

                .confirm-header {
                    text-align: center;
                    padding: 30px 30px 20px;
                }

                .confirm-icon {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 16px;
                    font-size: 24px;
                    color: white;
                }

                .confirm-icon.confirm-success {
                    background: #10b981;
                }

                .confirm-icon.confirm-error {
                    background: #ef4444;
                }

                .confirm-icon.confirm-warning {
                    background: #f59e0b;
                }

                .confirm-icon.confirm-info {
                    background: #3b82f6;
                }

                .confirm-header h3 {
                    margin: 0;
                    color: #111827;
                    font-size: 1.25rem;
                    font-weight: 600;
                }

                .confirm-body {
                    padding: 0 30px 20px;
                    text-align: center;
                }

                .confirm-body p {
                    margin: 0;
                    color: #6b7280;
                    line-height: 1.5;
                    font-size: 1rem;
                }

                .confirm-body .text-muted {
                    color: #9ca3af;
                    font-size: 0.85rem;
                    margin-top: 8px;
                    display: block;
                }

                .confirm-actions {
                    display: flex;
                    gap: 12px;
                    padding: 20px 30px 30px;
                    justify-content: center;
                }

                .confirm-actions .btn {
                    flex: 1;
                    max-width: 120px;
                }

                /* تصميم متجاوب للإشعارات */
                @media (max-width: 768px) {
                    .notification-container {
                        left: 10px;
                        right: 10px;
                        top: 90px;
                    }

                    .notification {
                        min-width: auto;
                        max-width: none;
                        width: 100%;
                    }

                    .confirm-content {
                        margin: 20px;
                        width: calc(100% - 40px);
                    }

                    .confirm-header {
                        padding: 20px 20px 15px;
                    }

                    .confirm-body {
                        padding: 0 20px 15px;
                    }

                    .confirm-actions {
                        padding: 15px 20px 20px;
                        flex-direction: column;
                    }

                    .confirm-actions .btn {
                        max-width: none;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }
}

// إنشاء مثيل من نظام الإشعارات
const notificationSystem = new NotificationSystem();

// دوال مساعدة سريعة
function showSuccess(message, duration) {
    return notificationSystem.showSuccess(message, duration);
}

function showError(message, duration) {
    return notificationSystem.showError(message, duration);
}

function showWarning(message, duration) {
    return notificationSystem.showWarning(message, duration);
}

function showInfo(message, duration) {
    return notificationSystem.showInfo(message, duration);
}

function showConfirm(message, onConfirm, onCancel, options) {
    return notificationSystem.showConfirm(message, onConfirm, onCancel, options);
}
