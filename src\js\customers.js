// إدارة العملاء
class CustomerManager {
    constructor() {
        this.customers = [];
        this.loadCustomers();
    }

    loadCustomers() {
        this.customers = storage.readFile('customers.json') || [];
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    addCustomer(customerData) {
        const customer = {
            id: this.generateId(),
            name: customerData.name,
            phone: customerData.phone,
            address: customerData.address || '',
            email: customerData.email || '',
            notes: customerData.notes || '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (storage.addItem('customers.json', customer)) {
            this.loadCustomers();
            return { success: true, customer };
        }
        return { success: false, message: 'فشل في إضافة العميل' };
    }

    updateCustomer(id, customerData) {
        const updatedData = {
            name: customerData.name,
            phone: customerData.phone,
            address: customerData.address || '',
            email: customerData.email || '',
            notes: customerData.notes || '',
            updatedAt: new Date().toISOString()
        };

        if (storage.updateItem('customers.json', id, updatedData)) {
            this.loadCustomers();
            return { success: true };
        }
        return { success: false, message: 'فشل في تحديث العميل' };
    }

    deleteCustomer(id) {
        if (storage.deleteItem('customers.json', id)) {
            this.loadCustomers();
            return { success: true };
        }
        return { success: false, message: 'فشل في حذف العميل' };
    }

    searchCustomers(searchTerm) {
        return storage.searchItems('customers.json', searchTerm, ['name', 'phone', 'address', 'email']);
    }

    getCustomerById(id) {
        return storage.getItemById('customers.json', id);
    }
}

const customerManager = new CustomerManager();

// تحميل صفحة العملاء
function loadCustomers() {
    const customers = customerManager.customers;
    const customersHTML = `
        <div class="customers-container">
            <div class="search-bar">
                <div class="search-input-group">
                    <i class="fas fa-search"></i>
                    <input type="text" id="customerSearch" placeholder="البحث في العملاء..." onkeyup="searchCustomers()">
                </div>
            </div>

            <div class="table-container customers-table">
                <table>
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>رقم الهاتف</th>
                            <th>العنوان</th>
                            <th>البريد الإلكتروني</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="customersTableBody">
                        ${customers.map(customer => `
                            <tr data-id="${customer.id}">
                                <td>
                                    <div class="customer-name">
                                        <i class="fas fa-user-circle"></i>
                                        <span>${customer.name}</span>
                                    </div>
                                </td>
                                <td>${customer.phone}</td>
                                <td>${customer.address || '-'}</td>
                                <td>${customer.email || '-'}</td>
                                <td>${new Date(customer.createdAt).toLocaleDateString('ar-EG')}</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-btn edit" onclick="editCustomer('${customer.id}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-btn delete" onclick="deleteCustomer('${customer.id}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button class="table-btn view" onclick="viewCustomerDetails('${customer.id}')" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                
                ${customers.length === 0 ? '<div class="no-data">لا توجد عملاء مسجلين</div>' : ''}
            </div>
        </div>

        <!-- نافذة إضافة/تعديل العميل -->
        <div id="customerModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="customerModalTitle">إضافة عميل جديد</h2>
                    <button class="close-btn" onclick="closeCustomerModal()">&times;</button>
                </div>
                
                <form id="customerForm" onsubmit="saveCustomer(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerName">اسم العميل *</label>
                            <input type="text" id="customerName" required>
                        </div>
                        <div class="form-group">
                            <label for="customerPhone">رقم الهاتف *</label>
                            <input type="tel" id="customerPhone" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerEmail">البريد الإلكتروني</label>
                            <input type="email" id="customerEmail">
                        </div>
                        <div class="form-group">
                            <label for="customerAddress">العنوان</label>
                            <input type="text" id="customerAddress">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="customerNotes">ملاحظات</label>
                        <textarea id="customerNotes" rows="3"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeCustomerModal()">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- نافذة عرض تفاصيل العميل -->
        <div id="customerDetailsModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>تفاصيل العميل</h2>
                    <button class="close-btn" onclick="closeCustomerDetailsModal()">&times;</button>
                </div>
                
                <div class="customer-details" id="customerDetailsContent">
                    <!-- سيتم تحميل التفاصيل هنا -->
                </div>
            </div>
        </div>
    `;

    document.getElementById('customersContent').innerHTML = customersHTML;
    addCustomerStyles();
}

// البحث في العملاء
function searchCustomers() {
    const searchTerm = document.getElementById('customerSearch').value;
    const customers = searchTerm ? customerManager.searchCustomers(searchTerm) : customerManager.customers;
    
    const tableBody = document.getElementById('customersTableBody');
    tableBody.innerHTML = customers.map(customer => `
        <tr data-id="${customer.id}">
            <td>
                <div class="customer-name">
                    <i class="fas fa-user-circle"></i>
                    <span>${customer.name}</span>
                </div>
            </td>
            <td>${customer.phone}</td>
            <td>${customer.address || '-'}</td>
            <td>${customer.email || '-'}</td>
            <td>${new Date(customer.createdAt).toLocaleDateString('ar-EG')}</td>
            <td>
                <div class="table-actions">
                    <button class="table-btn edit" onclick="editCustomer('${customer.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="table-btn delete" onclick="deleteCustomer('${customer.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="table-btn view" onclick="viewCustomerDetails('${customer.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// إظهار نافذة إضافة عميل
function showAddCustomerModal() {
    document.getElementById('customerModalTitle').textContent = 'إضافة عميل جديد';
    document.getElementById('customerForm').reset();
    document.getElementById('customerForm').removeAttribute('data-edit-id');
    document.getElementById('customerModal').style.display = 'flex';
}

// تعديل عميل
function editCustomer(id) {
    const customer = customerManager.getCustomerById(id);
    if (customer) {
        document.getElementById('customerModalTitle').textContent = 'تعديل العميل';
        document.getElementById('customerName').value = customer.name;
        document.getElementById('customerPhone').value = customer.phone;
        document.getElementById('customerEmail').value = customer.email || '';
        document.getElementById('customerAddress').value = customer.address || '';
        document.getElementById('customerNotes').value = customer.notes || '';
        document.getElementById('customerForm').setAttribute('data-edit-id', id);
        document.getElementById('customerModal').style.display = 'flex';
    }
}

// حفظ العميل
function saveCustomer(event) {
    event.preventDefault();
    
    const formData = {
        name: document.getElementById('customerName').value,
        phone: document.getElementById('customerPhone').value,
        email: document.getElementById('customerEmail').value,
        address: document.getElementById('customerAddress').value,
        notes: document.getElementById('customerNotes').value
    };

    const editId = document.getElementById('customerForm').getAttribute('data-edit-id');
    let result;

    if (editId) {
        result = customerManager.updateCustomer(editId, formData);
    } else {
        result = customerManager.addCustomer(formData);
    }

    if (result.success) {
        closeCustomerModal();
        loadCustomers();
        alert(editId ? 'تم تحديث العميل بنجاح' : 'تم إضافة العميل بنجاح');
    } else {
        alert(result.message);
    }
}

// حذف عميل
function deleteCustomer(id) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
        const result = customerManager.deleteCustomer(id);
        if (result.success) {
            loadCustomers();
            alert('تم حذف العميل بنجاح');
        } else {
            alert(result.message);
        }
    }
}

// عرض تفاصيل العميل
function viewCustomerDetails(id) {
    const customer = customerManager.getCustomerById(id);
    if (customer) {
        const detailsHTML = `
            <div class="customer-info">
                <div class="info-section">
                    <h3><i class="fas fa-user"></i> المعلومات الأساسية</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>الاسم:</label>
                            <span>${customer.name}</span>
                        </div>
                        <div class="info-item">
                            <label>رقم الهاتف:</label>
                            <span>${customer.phone}</span>
                        </div>
                        <div class="info-item">
                            <label>البريد الإلكتروني:</label>
                            <span>${customer.email || 'غير محدد'}</span>
                        </div>
                        <div class="info-item">
                            <label>العنوان:</label>
                            <span>${customer.address || 'غير محدد'}</span>
                        </div>
                    </div>
                </div>
                
                ${customer.notes ? `
                    <div class="info-section">
                        <h3><i class="fas fa-sticky-note"></i> الملاحظات</h3>
                        <p>${customer.notes}</p>
                    </div>
                ` : ''}
                
                <div class="info-section">
                    <h3><i class="fas fa-calendar"></i> معلومات التسجيل</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>تاريخ التسجيل:</label>
                            <span>${new Date(customer.createdAt).toLocaleDateString('ar-EG')}</span>
                        </div>
                        <div class="info-item">
                            <label>آخر تحديث:</label>
                            <span>${new Date(customer.updatedAt).toLocaleDateString('ar-EG')}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('customerDetailsContent').innerHTML = detailsHTML;
        document.getElementById('customerDetailsModal').style.display = 'flex';
    }
}

// إغلاق نوافذ العملاء
function closeCustomerModal() {
    document.getElementById('customerModal').style.display = 'none';
}

function closeCustomerDetailsModal() {
    document.getElementById('customerDetailsModal').style.display = 'none';
}

// إضافة أنماط العملاء
function addCustomerStyles() {
    if (!document.getElementById('customerStyles')) {
        const style = document.createElement('style');
        style.id = 'customerStyles';
        style.textContent = `
            .customers-container { padding: 0; }
            .customers-table {
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                border: 1px solid #f3f4f6;
            }
            .customers-table table {
                width: 100%;
                border-collapse: collapse;
            }
            .customers-table th {
                background: #6366f1;
                color: white;
                padding: 16px 20px;
                text-align: right;
                font-weight: 600;
                font-size: 0.9rem;
                border: none;
            }
            .customers-table td {
                padding: 16px 20px;
                border-bottom: 1px solid #f3f4f6;
                vertical-align: middle;
                font-size: 0.9rem;
            }
            .customers-table tr:hover {
                background: #f8fafc;
            }
            .customers-table tr:last-child td {
                border-bottom: none;
            }
            .customer-name {
                display: flex;
                align-items: center;
                gap: 12px;
                font-weight: 500;
            }
            .customer-name i {
                color: #6366f1;
                font-size: 1.1rem;
                width: 20px;
                text-align: center;
            }
            .action-buttons {
                display: flex;
                gap: 6px;
                justify-content: center;
            }
            .btn-icon {
                width: 32px;
                height: 32px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
                font-size: 0.8rem;
            }
            .btn-icon.edit {
                background: #fef3c7;
                color: #d97706;
            }
            .btn-icon.edit:hover {
                background: #fde68a;
            }
            .btn-icon.delete {
                background: #fee2e2;
                color: #dc2626;
            }
            .btn-icon.delete:hover {
                background: #fecaca;
            }
            .btn-icon.view {
                background: #dcfce7;
                color: #16a34a;
            }
            .btn-icon.view:hover {
                background: #bbf7d0;
            }
            .no-data {
                text-align: center;
                padding: 60px 20px;
                color: #6b7280;
                font-size: 1rem;
                background: white;
                border-radius: 12px;
                border: 1px solid #f3f4f6;
            }
            .customer-info { padding: 25px; }
            .info-section { margin-bottom: 25px; }
            .info-section h3 { color: #333; margin-bottom: 15px; display: flex; align-items: center; gap: 10px; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
            .info-item { display: flex; flex-direction: column; gap: 5px; }
            .info-item label { font-weight: 600; color: #666; font-size: 0.9rem; }
            .info-item span { color: #333; }
        `;
        document.head.appendChild(style);
    }
}
