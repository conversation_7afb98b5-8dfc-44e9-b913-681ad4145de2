/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
}

/* شاشة تسجيل الدخول */
.login-screen {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.login-screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.login-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo-container {
    margin-bottom: 20px;
}

.logo-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 15px;
    display: block;
}

.login-header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.login-header p {
    color: #666;
    font-size: 1rem;
    font-weight: 400;
}

/* نموذج تسجيل الدخول */
.login-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 15px;
    color: #667eea;
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 15px 45px 15px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.toggle-password {
    left: 15px !important;
    right: auto !important;
    cursor: pointer;
    color: #999 !important;
}

.toggle-password:hover {
    color: #667eea !important;
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-options {
    text-align: center;
    margin-top: 20px;
}

.login-options a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.login-options a:hover {
    color: #764ba2;
}

.error-message {
    background: #fee;
    color: #c33;
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    display: none;
    border: 1px solid #fcc;
}

/* الواجهة الرئيسية */
.main-app {
    display: flex;
    height: 100vh;
    background: #f8fafc;
}

/* شريط التنقل العلوي */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0 30px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.3rem;
    font-weight: 700;
}

.nav-brand i {
    font-size: 1.5rem;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    margin-top: 70px;
    overflow-y: auto;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
    position: fixed;
    top: 70px;
    right: 0;
    bottom: 0;
    overflow-y: auto;
}

.sidebar-menu {
    list-style: none;
    padding: 20px 0;
}

.menu-item {
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
    color: #64748b;
    font-weight: 500;
}

.menu-item:hover {
    background: #f1f5f9;
    color: #667eea;
}

.menu-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
}

.menu-item.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #fff;
}

.menu-item i {
    font-size: 1.1rem;
    width: 20px;
}

/* منطقة المحتوى */
#pageContent {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background: #f8fafc;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* أنماط إضافية للجداول والنماذج */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f1f5f9;
}

.page-header h1 {
    font-size: 2rem;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.btn {
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.btn-primary:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: linear-gradient(145deg, #f7fafc 0%, #edf2f7 100%);
    color: #4a5568;
    border: 2px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: linear-gradient(145deg, #edf2f7 0%, #e2e8f0 100%);
    border-color: #cbd5e0;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 101, 101, 0.3);
}

.btn-danger:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(245, 101, 101, 0.4);
}

.btn i {
    font-size: 1.1rem;
}

/* أنماط النوافذ المنبثقة (Modal) */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    backdrop-filter: blur(10px);
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.4s ease-out;
}

.modal-content {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    max-width: 650px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
    animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 30px 35px 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.6rem;
    font-weight: 700;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    gap: 12px;
}

.modal-header h2::before {
    content: '📦';
    font-size: 1.4rem;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    font-size: 1.5rem;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg) scale(1.1);
}

/* أنماط النماذج */
.modal-content form {
    padding: 35px;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    overflow-y: auto;
    max-height: calc(90vh - 140px);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #2d3748;
    font-size: 0.95rem;
    position: relative;
    padding-right: 8px;
}

.form-group label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    right: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.form-group:focus-within label::after {
    width: 100%;
}

.form-control, .form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 15px 18px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%);
    font-family: 'Cairo', sans-serif;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
}

.form-control:focus, .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow:
        0 0 0 3px rgba(102, 126, 234, 0.1),
        inset 0 1px 3px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
    background: #ffffff;
}

.form-group select {
    cursor: pointer;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23667eea" stroke-width="2"><polyline points="6,9 12,15 18,9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: left 15px center;
    background-size: 16px;
    padding-left: 45px;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
    font-family: 'Cairo', sans-serif;
}

.form-actions {
    padding: 25px 35px 35px;
    background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    border-top: 1px solid rgba(102, 126, 234, 0.1);
    margin: 0 -35px -35px;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .login-container {
        margin: 20px;
        padding: 30px 25px;
    }

    .sidebar {
        width: 250px;
    }

    .navbar {
        padding: 0 20px;
    }

    #pageContent {
        padding: 20px;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-content {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-header {
        padding: 20px 20px 15px;
    }

    .form-actions {
        padding: 15px 20px 20px;
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }
}

/* تحسينات إضافية للنافذة */
.form-group input[required] + .required-indicator,
.form-group select[required] + .required-indicator {
    position: absolute;
    top: 8px;
    left: 8px;
    color: #e53e3e;
    font-size: 0.8rem;
    font-weight: bold;
}

.form-group input:valid,
.form-group select:valid {
    border-color: #48bb78;
}

.form-group input:invalid:not(:placeholder-shown),
.form-group select:invalid:not(:placeholder-shown) {
    border-color: #f56565;
}

/* تأثيرات تفاعلية للحقول */
.form-group {
    position: relative;
}

.form-group::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: -1;
}

.form-group:focus-within::before {
    opacity: 1;
}

/* تحسين مظهر النص المساعد */
.form-help {
    font-size: 0.85rem;
    color: #718096;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.form-help i {
    color: #667eea;
}

/* تحسين مظهر الأخطاء */
.form-error {
    color: #e53e3e;
    font-size: 0.85rem;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.form-error i {
    color: #e53e3e;
}

/* تأثير التحميل للأزرار */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات إضافية للنافذة */
.modal {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal[style*="flex"] {
    opacity: 1;
}

/* تأثيرات hover للحقول */
.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: #cbd5e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* تحسين مظهر placeholder */
.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #a0aec0;
    font-style: italic;
}

/* تأثير النجاح للحقول */
.form-group input.success,
.form-group select.success {
    border-color: #48bb78;
    background: linear-gradient(145deg, #f0fff4 0%, #ffffff 100%);
}

/* تأثير الخطأ للحقول */
.form-group input.error,
.form-group select.error {
    border-color: #f56565;
    background: linear-gradient(145deg, #fff5f5 0%, #ffffff 100%);
}

/* تحسين شكل الأيقونات في النموذج */
.form-help i,
.form-error i {
    width: 16px;
    text-align: center;
}

/* تأثير التركيز المحسن */
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #667eea;
    box-shadow:
        0 0 0 3px rgba(102, 126, 234, 0.1),
        0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
}

/* تحسين شكل النص في العنوان */
.modal-header h2 {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* تأثير الضغط على الأزرار */
.btn:active {
    transform: translateY(0) scale(0.98);
}

/* تحسين شكل النافذة على الشاشات الصغيرة */
@media (max-width: 480px) {
    .modal-content {
        width: 95%;
        margin: 5px;
        border-radius: 15px;
    }

    .modal-header {
        padding: 20px 25px 15px;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    .modal-content form {
        padding: 25px;
    }

    .form-actions {
        padding: 20px 25px 25px;
        margin: 0 -25px -25px;
    }
}
