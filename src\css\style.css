/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
}

/* شاشة تسجيل الدخول */
.login-screen {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.login-screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.login-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo-container {
    margin-bottom: 20px;
}

.logo-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 15px;
    display: block;
}

.login-header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.login-header p {
    color: #666;
    font-size: 1rem;
    font-weight: 400;
}

/* نموذج تسجيل الدخول */
.login-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 15px;
    color: #667eea;
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 15px 45px 15px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.toggle-password {
    left: 15px !important;
    right: auto !important;
    cursor: pointer;
    color: #999 !important;
}

.toggle-password:hover {
    color: #667eea !important;
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-options {
    text-align: center;
    margin-top: 20px;
}

.login-options a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.login-options a:hover {
    color: #764ba2;
}

.error-message {
    background: #fee;
    color: #c33;
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    display: none;
    border: 1px solid #fcc;
}

/* الواجهة الرئيسية */
.main-app {
    display: flex;
    height: 100vh;
    background: #f8fafc;
}

/* شريط التنقل العلوي */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0 30px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.3rem;
    font-weight: 700;
}

.nav-brand i {
    font-size: 1.5rem;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    margin-top: 70px;
    overflow-y: auto;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
    position: fixed;
    top: 70px;
    right: 0;
    bottom: 0;
    overflow-y: auto;
}

.sidebar-menu {
    list-style: none;
    padding: 20px 0;
}

.menu-item {
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
    color: #64748b;
    font-weight: 500;
}

.menu-item:hover {
    background: #f1f5f9;
    color: #667eea;
}

.menu-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
}

.menu-item.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #fff;
}

.menu-item i {
    font-size: 1.1rem;
    width: 20px;
}

/* منطقة المحتوى */
#pageContent {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background: #f8fafc;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* أنماط إضافية للجداول والنماذج */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f1f5f9;
}

.page-header h1 {
    font-size: 2rem;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

/* أنماط الأزرار - تصميم بسيط وثابت */
.btn, .action-btn, button {
    padding: 12px 20px !important;
    border: none !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    font-size: 0.9rem !important;
    font-family: 'Cairo', sans-serif !important;
    box-sizing: border-box !important;
    min-height: 44px !important;
}

.btn-primary, .action-btn {
    background: #6366f1 !important;
    color: white !important;
    border: 1px solid #6366f1 !important;
}

.btn-primary:hover, .action-btn:hover {
    background: #5b21b6 !important;
    border-color: #5b21b6 !important;
    transform: none !important;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3) !important;
}

.btn-secondary {
    background: #ffffff !important;
    color: #6b7280 !important;
    border: 1px solid #d1d5db !important;
}

.btn-secondary:hover {
    background: #f9fafb !important;
    border-color: #9ca3af !important;
}

.btn-success {
    background: #10b981 !important;
    color: white !important;
    border: 1px solid #10b981 !important;
}

.btn-success:hover {
    background: #059669 !important;
    border-color: #059669 !important;
}

.btn-danger {
    background: #ef4444 !important;
    color: white !important;
    border: 1px solid #ef4444 !important;
}

.btn-danger:hover {
    background: #dc2626 !important;
    border-color: #dc2626 !important;
}

.btn i, .action-btn i {
    font-size: 1rem !important;
}

/* إصلاح تضارب الأنماط للأزرار */
.quick-actions .action-btn {
    background: #6366f1 !important;
    color: white !important;
    border: 1px solid #6366f1 !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    font-size: 0.9rem !important;
    font-family: 'Cairo', sans-serif !important;
    box-sizing: border-box !important;
    min-height: 44px !important;
    transform: none !important;
    box-shadow: none !important;
}

.quick-actions .action-btn:hover {
    background: #5b21b6 !important;
    border-color: #5b21b6 !important;
    transform: none !important;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3) !important;
}

/* إصلاح أنماط الحاوية */
.action-buttons {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 15px !important;
}

.quick-actions {
    background: white !important;
    padding: 25px !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    margin-bottom: 20px !important;
}

.quick-actions h2 {
    font-size: 1.3rem !important;
    color: #374151 !important;
    margin-bottom: 20px !important;
    font-weight: 600 !important;
}

/* إزالة أي تأثيرات تضارب من JavaScript */
* {
    box-sizing: border-box;
}

/* ضمان ثبات الأزرار في جميع الحالات */
button, .btn, .action-btn, input[type="button"], input[type="submit"] {
    background: none !important;
    border: 1px solid #d1d5db !important;
    color: #6b7280 !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    font-size: 0.9rem !important;
    font-family: 'Cairo', sans-serif !important;
    min-height: 44px !important;
    transform: none !important;
    box-shadow: none !important;
}

/* إعادة تطبيق الألوان للأزرار المحددة */
.btn-primary, .action-btn, button.btn-primary {
    background: #6366f1 !important;
    color: white !important;
    border-color: #6366f1 !important;
}

.btn-primary:hover, .action-btn:hover, button.btn-primary:hover {
    background: #5b21b6 !important;
    border-color: #5b21b6 !important;
}

.btn-secondary, button.btn-secondary {
    background: #ffffff !important;
    color: #6b7280 !important;
    border-color: #d1d5db !important;
}

.btn-secondary:hover, button.btn-secondary:hover {
    background: #f9fafb !important;
    border-color: #9ca3af !important;
}

/* منع أي تأثيرات JavaScript على الأزرار */
.action-btn, .btn, button {
    background-image: none !important;
    background-attachment: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-color: #ffffff !important;
    background-position: initial !important;
    background-repeat: initial !important;
    background-size: initial !important;
}

.action-btn, .btn-primary, button.btn-primary {
    background-color: #6366f1 !important;
}

.action-btn:hover, .btn-primary:hover, button.btn-primary:hover {
    background-color: #5b21b6 !important;
}

/* إعادة تعيين شامل للأزرار */
.quick-actions .action-btn,
.action-buttons .action-btn,
div .action-btn {
    all: unset !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 12px 20px !important;
    background: #6366f1 !important;
    color: white !important;
    border: 1px solid #6366f1 !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    font-size: 0.9rem !important;
    font-family: 'Cairo', sans-serif !important;
    gap: 8px !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    min-height: 44px !important;
    box-sizing: border-box !important;
}

.quick-actions .action-btn:hover,
.action-buttons .action-btn:hover,
div .action-btn:hover {
    background: #5b21b6 !important;
    border-color: #5b21b6 !important;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3) !important;
}

/* أنماط النوافذ المنبثقة (Modal) - تصميم بسيط */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
    animation: slideIn 0.3s ease-out;
    border: 1px solid #e5e7eb;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 24px 32px;
    background: #ffffff;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #111827;
}

.close-btn {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    font-size: 1.2rem;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

/* أنماط النماذج - تصميم بسيط */
.modal-content form {
    padding: 32px;
    background: #ffffff;
    overflow-y: auto;
    max-height: calc(90vh - 140px);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
}

.form-control, .form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: #ffffff;
    font-family: 'Cairo', sans-serif;
}

.form-control:focus, .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group select {
    cursor: pointer;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%236b7280" stroke-width="2"><polyline points="6,9 12,15 18,9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: left 12px center;
    background-size: 16px;
    padding-left: 40px;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
    font-family: 'Cairo', sans-serif;
}

.form-actions {
    padding: 24px 32px;
    background: #f9fafb;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    border-top: 1px solid #f3f4f6;
    margin: 0 -32px -32px;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .login-container {
        margin: 20px;
        padding: 30px 25px;
    }

    .sidebar {
        width: 250px;
    }

    .navbar {
        padding: 0 20px;
    }

    #pageContent {
        padding: 20px;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-content {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-header {
        padding: 20px 20px 15px;
    }

    .form-actions {
        padding: 15px 20px 20px;
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }
}

/* تحسينات بسيطة للنافذة */
.form-help {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 4px;
}

.form-error {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: 4px;
}

/* تأثير التحميل للأزرار */
.btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين شكل النافذة على الشاشات الصغيرة */
@media (max-width: 480px) {
    .modal-content {
        width: 95%;
        margin: 5px;
    }

    .modal-header {
        padding: 20px 24px;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    .modal-content form {
        padding: 24px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .form-actions {
        padding: 20px 24px;
        margin: 0 -24px -24px;
        flex-direction: column;
    }
}
