// إدارة المنتجات
class ProductManager {
    constructor() {
        this.products = [];
        this.loadProducts();
    }

    loadProducts() {
        try {
            this.products = storage.readFile('products.json') || [];
        } catch (error) {
            console.error('خطأ في تحميل المنتجات:', error);
            this.products = [];
        }
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    addProduct(productData) {
        const product = {
            id: this.generateId(),
            name: productData.name,
            category: productData.category,
            price: parseFloat(productData.price),
            cost: parseFloat(productData.cost || 0),
            stock: parseInt(productData.stock || 0),
            description: productData.description || '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (storage.addItem('products.json', product)) {
            this.loadProducts();
            return { success: true, product };
        }
        return { success: false, message: 'فشل في إضافة المنتج' };
    }

    updateProduct(id, productData) {
        const updatedData = {
            name: productData.name,
            category: productData.category,
            price: parseFloat(productData.price),
            cost: parseFloat(productData.cost || 0),
            stock: parseInt(productData.stock || 0),
            description: productData.description || '',
            updatedAt: new Date().toISOString()
        };

        if (storage.updateItem('products.json', id, updatedData)) {
            this.loadProducts();
            return { success: true };
        }
        return { success: false, message: 'فشل في تحديث المنتج' };
    }

    deleteProduct(id) {
        if (storage.deleteItem('products.json', id)) {
            this.loadProducts();
            return { success: true };
        }
        return { success: false, message: 'فشل في حذف المنتج' };
    }

    searchProducts(searchTerm) {
        return storage.searchItems('products.json', searchTerm, ['name', 'category', 'description']);
    }

    getProductById(id) {
        return storage.getItemById('products.json', id);
    }

    updateStock(id, quantity) {
        const product = this.getProductById(id);
        if (product) {
            const newStock = Math.max(0, product.stock - quantity); // تأكد من عدم النزول تحت الصفر
            const result = this.updateProduct(id, { ...product, stock: newStock });

            // تسجيل حركة المخزون
            console.log(`تم خصم ${quantity} من المنتج ${product.name}. المخزون الجديد: ${newStock}`);

            return result;
        }
        return { success: false, message: 'المنتج غير موجود' };
    }
}

const productManager = new ProductManager();

// تحميل صفحة المنتجات
function loadProducts() {
    const products = productManager.products;
    const productsHTML = `
        <div class="products-container">
            <div class="page-header">
                <h2>إدارة المنتجات</h2>
              
            <div class="view-options-bar">
                <div class="view-options">
                    <button class="view-option active" data-view="grid" onclick="changeProductView('grid')">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button class="view-option" data-view="list" onclick="changeProductView('list')">
                        <i class="fas fa-th-list"></i>
                    </button>
                    <button class="view-option" data-view="compact" onclick="changeProductView('compact')">
                        <i class="fas fa-grip-horizontal"></i>
                    </button>
                    <button class="view-option" data-view="table" onclick="changeProductView('table')">
                        <i class="fas fa-table"></i>
                    </button>
                </div>
            </div>
            </div>
            
            
            <div id="productsContainer" class="view-grid">
                <!-- سيتم تحميل المنتجات هنا -->
            </div>
        </div>

        <!-- نافذة إضافة/تعديل المنتج -->
        <div id="productModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modalTitle">إضافة منتج جديد</h2>
                    <button class="close-btn" onclick="closeProductModal()">&times;</button>
                </div>

                <form id="productForm" onsubmit="saveProduct(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productName">اسم المنتج *</label>
                            <input type="text" id="productName" placeholder="أدخل اسم المنتج" required>
                        </div>
                        <div class="form-group">
                            <label for="productCategory">الفئة *</label>
                            <select id="productCategory" required>
                                <option value="">اختر الفئة</option>
                                <option value="كنب">كنب</option>
                                <option value="طاولات">طاولات</option>
                                <option value="كراسي">كراسي</option>
                                <option value="خزائن">خزائن</option>
                                <option value="أسرة">أسرة</option>
                                <option value="ديكورات">ديكورات</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="productPrice">سعر البيع *</label>
                            <input type="number" id="productPrice" step="0.01" min="0" placeholder="0.00" required>
                        </div>
                        <div class="form-group">
                            <label for="productCost">سعر التكلفة</label>
                            <input type="number" id="productCost" step="0.01" min="0" placeholder="0.00">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="productStock">الكمية في المخزون</label>
                        <input type="number" id="productStock" min="0" value="0" placeholder="0">
                    </div>

                    <div class="form-group">
                        <label for="productDescription">الوصف</label>
                        <textarea id="productDescription" rows="3" placeholder="وصف تفصيلي للمنتج (اختياري)"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeProductModal()">
                            إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary">
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('productsContent').innerHTML = productsHTML;
    addProductStyles();
    renderProductsView(productManager.products);

    // إضافة معالج إغلاق النافذة عند النقر خارجها
    setTimeout(() => {
        const modal = document.getElementById('productModal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeProductModal();
                }
            });
        }
    }, 100);
}

// تغيير طريقة عرض المنتجات
function changeProductView(viewType) {
    // تحديث الزر النشط
    document.querySelectorAll('.view-option').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`.view-option[data-view="${viewType}"]`).classList.add('active');

    // تحديث كلاس الحاوية
    const container = document.getElementById('productsContainer');
    container.className = `view-${viewType}`;

    // إعادة عرض المنتجات
    renderProductsView(productManager.products);
}

// عرض المنتجات حسب طريقة العرض المحددة
function renderProductsView(products) {
    const container = document.getElementById('productsContainer');
    const viewType = container.className.replace('view-', '');
    
    if (viewType === 'table') {
        renderProductsTable(products, container);
    } else {
        renderProductsCards(products, container, viewType);
    }
}

// عرض المنتجات كجدول
function renderProductsTable(products, container) {
    container.innerHTML = `
        <table class="products-table">
            <thead>
                <tr>
                    <th>اسم المنتج</th>
                    <th>الفئة</th>
                    <th>السعر</th>
                    <th>المخزون</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${products.map(product => `
                    <tr data-id="${product.id}">
                        <td>${product.name}</td>
                        <td>${product.category}</td>
                        <td class="price">${product.price.toLocaleString()} جنيه</td>
                        <td class="stock ${product.stock < 5 ? 'low' : ''}">${product.stock} قطعة</td>
                        <td class="actions">
                            <button class="btn-icon edit" onclick="editProduct('${product.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon delete" onclick="deleteProduct('${product.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

// عرض المنتجات كبطاقات
function renderProductsCards(products, container, viewType) {
    container.innerHTML = products.map(product => {
        if (viewType === 'compact') {
            return `
                <div class="product-card compact" data-id="${product.id}">
                    <div class="product-header">
                        <h3>${product.name}</h3>
                        <div class="product-actions">
                            <button class="btn-icon edit" onclick="editProduct('${product.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon delete" onclick="deleteProduct('${product.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="product-brief">
                        <span class="category">${product.category}</span>
                        <span class="price">${product.price.toLocaleString()} جنيه</span>
                        <span class="stock ${product.stock < 5 ? 'low' : ''}">${product.stock}</span>
                    </div>
                </div>
            `;
        } else {
            return `
                <div class="product-card" data-id="${product.id}">
                    <div class="product-header">
                        <h3>${product.name}</h3>
                        <div class="product-actions">
                            <button class="btn-icon edit" onclick="editProduct('${product.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon delete" onclick="deleteProduct('${product.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="product-info">
                        <div class="info-row">
                            <span class="label">الفئة:</span>
                            <span class="value">${product.category}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">السعر:</span>
                            <span class="value price">${product.price.toLocaleString()} جنيه</span>
                        </div>
                        <div class="info-row">
                            <span class="label">المخزون:</span>
                            <span class="value stock ${product.stock < 5 ? 'low' : ''}">${product.stock} قطعة</span>
                        </div>
                    </div>
                    
                    ${product.description ? `
                        <div class="product-description">
                            <p>${product.description}</p>
                        </div>
                    ` : ''}
                </div>
            `;
        }
    }).join('');
}



// إظهار نافذة إضافة منتج
function showAddProductModal() {
    try {
        const modal = document.getElementById('productModal');
        const modalTitle = document.getElementById('modalTitle');
        const productForm = document.getElementById('productForm');

        if (!modal || !modalTitle || !productForm) {
            console.error('عناصر النافذة المنبثقة غير موجودة');
            return;
        }

        // تحديث عنوان النافذة
        modalTitle.textContent = 'إضافة منتج جديد';
        productForm.reset();
        productForm.removeAttribute('data-edit-id');

        // إظهار النافذة مع تأثير
        modal.style.display = 'flex';

        // إضافة تأثير الظهور
        setTimeout(() => {
            modal.style.opacity = '1';
        }, 10);

        // التركيز على أول حقل مع تأخير للتأثير
        setTimeout(() => {
            const firstInput = document.getElementById('productName');
            if (firstInput) {
                firstInput.focus();
                firstInput.select();
            }
        }, 300);

        // إضافة معالج بسيط للتنقل بـ Enter
        addSimpleEnterHandlers();

    } catch (error) {
        console.error('خطأ في إظهار نافذة إضافة المنتج:', error);
        if (typeof showError === 'function') {
            showError('حدث خطأ في فتح نافذة إضافة المنتج');
        } else {
            alert('حدث خطأ في فتح نافذة إضافة المنتج. يرجى المحاولة مرة أخرى.');
        }
    }
}

// إضافة معالج بسيط للتنقل بـ Enter
function addSimpleEnterHandlers() {
    const inputs = document.querySelectorAll('#productForm input, #productForm select');
    inputs.forEach((input, index) => {
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && input.type !== 'submit') {
                e.preventDefault();
                const nextInput = inputs[index + 1];
                if (nextInput) {
                    nextInput.focus();
                }
            }
        });
    });
}

// تعديل منتج
function editProduct(id) {
    const product = productManager.getProductById(id);
    if (product) {
        document.getElementById('modalTitle').textContent = 'تعديل المنتج';
        document.getElementById('productName').value = product.name;
        document.getElementById('productCategory').value = product.category;
        document.getElementById('productPrice').value = product.price;
        document.getElementById('productCost').value = product.cost;
        document.getElementById('productStock').value = product.stock;
        document.getElementById('productDescription').value = product.description;
        document.getElementById('productForm').setAttribute('data-edit-id', id);
        document.getElementById('productModal').style.display = 'flex';
    }
}

// حفظ المنتج
function saveProduct(event) {
    event.preventDefault();

    try {
        // التحقق من وجود العناصر
        const nameInput = document.getElementById('productName');
        const categoryInput = document.getElementById('productCategory');
        const priceInput = document.getElementById('productPrice');
        const costInput = document.getElementById('productCost');
        const stockInput = document.getElementById('productStock');
        const descriptionInput = document.getElementById('productDescription');

        if (!nameInput || !categoryInput || !priceInput) {
            alert('خطأ: لا يمكن العثور على حقول النموذج المطلوبة');
            return;
        }

        // التحقق من صحة البيانات
        if (!nameInput.value.trim()) {
            alert('يرجى إدخال اسم المنتج');
            nameInput.focus();
            return;
        }

        if (!categoryInput.value) {
            alert('يرجى اختيار فئة المنتج');
            categoryInput.focus();
            return;
        }

        if (!priceInput.value || parseFloat(priceInput.value) <= 0) {
            alert('يرجى إدخال سعر صحيح للمنتج');
            priceInput.focus();
            return;
        }

        const formData = {
            name: nameInput.value.trim(),
            category: categoryInput.value,
            price: priceInput.value,
            cost: costInput ? costInput.value : 0,
            stock: stockInput ? stockInput.value : 0,
            description: descriptionInput ? descriptionInput.value.trim() : ''
        };

        const editId = document.getElementById('productForm').getAttribute('data-edit-id');
        let result;

        if (editId) {
            result = productManager.updateProduct(editId, formData);
        } else {
            result = productManager.addProduct(formData);
        }

        handleSaveResult(result, editId);
    } catch (error) {
        console.error('خطأ في حفظ المنتج:', error);
        if (typeof showError === 'function') {
            showError('حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.');
        } else {
            alert('حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.');
        }
    }
}

// معالجة نتيجة الحفظ
function handleSaveResult(result, editId) {

    if (result && result.success) {
        // إظهار رسالة نجاح
        if (typeof showSuccess === 'function') {
            showSuccess(editId ? 'تم تحديث المنتج بنجاح ✅' : 'تم إضافة المنتج بنجاح ✅');
        } else {
            alert(editId ? 'تم تحديث المنتج بنجاح' : 'تم إضافة المنتج بنجاح');
        }

        closeProductModal();
        loadProducts();
    } else {
        // إظهار رسالة خطأ
        if (typeof showError === 'function') {
            showError(result ? result.message : 'حدث خطأ أثناء حفظ المنتج');
        } else {
            alert(result ? result.message : 'حدث خطأ أثناء حفظ المنتج');
        }
    }
}

// حذف منتج
function deleteProduct(id) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        const result = productManager.deleteProduct(id);
        if (result.success) {
            loadProducts();
            alert('تم حذف المنتج بنجاح');
        } else {
            alert(result.message);
        }
    }
}

// إغلاق نافذة المنتج
function closeProductModal() {
    try {
        const modal = document.getElementById('productModal');
        if (modal) {
            modal.style.display = 'none';

            // مسح النموذج
            const form = document.getElementById('productForm');
            if (form) {
                form.reset();
                form.removeAttribute('data-edit-id');

                // إزالة رسائل الخطأ
                const errorMessages = form.querySelectorAll('.form-error');
                errorMessages.forEach(error => error.remove());
            }
        }
    } catch (error) {
        console.error('خطأ في إغلاق نافذة المنتج:', error);
    }
}

// إضافة أنماط CSS للعرض المختلف
function addProductStyles() {
    if (!document.getElementById('productStyles')) {
        const style = document.createElement('style');
        style.id = 'productStyles';
        style.textContent = `
            .products-container { padding: 0; }
            .view-options-bar { margin-bottom: 25px; display: flex; justify-content: flex-end; }
            
            /* أزرار تبديل طريقة العرض */
            .view-options { display: flex; gap: 5px; }
            .view-option { width: 40px; height: 40px; border: 1px solid #e1e5e9; background: white; border-radius: 8px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease; }
            .view-option:hover { background: #f5f8fa; }
            .view-option.active { background: #4a6cf7; color: white; border-color: #4a6cf7; }
            
            /* عرض الشبكة (الافتراضي) */
            .view-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 20px; }
            
            /* عرض القائمة */
            .view-list { display: flex; flex-direction: column; gap: 15px; }
            .view-list .product-card { display: flex; flex-direction: column; }
            
            /* عرض مدمج */
            .view-compact { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 15px; }
            .view-compact .product-card { padding: 15px; }
            .product-card.compact .product-brief { display: flex; justify-content: space-between; margin-top: 10px; font-size: 0.9rem; }
            .product-card.compact .category { color: #666; }
            .product-card.compact .price { color: #2e7d32; font-weight: 600; }
            .product-card.compact .stock { padding: 2px 8px; border-radius: 10px; background: #e8f5e9; color: #2e7d32; }
            .product-card.compact .stock.low { background: #fff3e0; color: #e65100; }
            
            /* عرض الجدول - تصميم بسيط ومتسق */
            .products-table {
                width: 100%;
                border-collapse: collapse;
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                border: 1px solid #f3f4f6;
            }
            .products-table th, .products-table td {
                padding: 16px 20px;
                text-align: right;
                vertical-align: middle;
                font-size: 0.9rem;
            }
            .products-table th {
                background: #6366f1;
                font-weight: 600;
                color: white;
                border: none;
            }
            .products-table tr {
                border-bottom: 1px solid #f3f4f6;
            }
            .products-table tr:hover {
                background: #f8fafc;
            }
            .products-table tr:last-child {
                border-bottom: none;
            }
            .products-table td.price {
                color: #16a34a;
                font-weight: 600;
            }
            .products-table td.stock {
                font-weight: 600;
                color: #374151;
            }
            .products-table td.stock.low {
                color: #dc2626;
            }
            .products-table td.actions {
                display: flex;
                gap: 6px;
                justify-content: center;
            }
            
            /* أنماط البطاقات الحالية */
            .product-card { background: white; border-radius: 15px; padding: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); transition: transform 0.3s ease; }
            .product-card:hover { transform: translateY(-5px); }
            .product-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
            .product-header h3 { color: #333; font-size: 1.2rem; margin: 0; }
            .product-actions { display: flex; gap: 8px; }
            .btn-icon { width: 35px; height: 35px; border: none; border-radius: 8px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; }
            .btn-icon.edit { background: #e3f2fd; color: #1976d2; }
            .btn-icon.delete { background: #ffebee; color: #d32f2f; }
            .btn-icon:hover { transform: scale(1.1); }
            .product-info { margin-bottom: 15px; }
            .info-row { display: flex; justify-content: space-between; margin-bottom: 8px; }
            .info-row .label { color: #666; font-weight: 500; }
            .info-row .value { color: #333; font-weight: 600; }
            .info-row .price { color: #2e7d32; }
            .info-row .stock.low { color: #d32f2f; }
            .product-description { padding-top: 15px; border-top: 1px solid #eee; }
            .product-description p { color: #666; margin: 0; font-size: 0.9rem; }
            
            /* باقي الأنماط الحالية */
        `;
        document.head.appendChild(style);
    }
}


